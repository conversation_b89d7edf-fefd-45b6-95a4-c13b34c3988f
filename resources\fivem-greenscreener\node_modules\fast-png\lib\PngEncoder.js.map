{"version": 3, "file": "PngEncoder.js", "sourceRoot": "", "sources": ["../src/PngEncoder.ts"], "names": [], "mappings": ";;AAAA,uCAAoC;AACpC,+BAA+B;AAE/B,uCAAyC;AACzC,mDAAqD;AACrD,yCAA4C;AAC5C,mDAKyB;AAUzB,MAAM,kBAAkB,GAA2B;IACjD,KAAK,EAAE,CAAC;CACT,CAAC;AAYF,MAAqB,UAAW,SAAQ,mBAAQ;IAC7B,IAAI,CAAc;IAClB,YAAY,CAAyB;IAC9C,UAAU,CAAY;IACb,gBAAgB,CAAkB;IACnD,YAAmB,IAAe,EAAE,UAA6B,EAAE;QACjE,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,UAAU,GAAG,yBAAS,CAAC,OAAO,CAAC;QACpC,IAAI,CAAC,YAAY,GAAG,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;QAC/D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAElC,IAAI,CAAC,gBAAgB;YACnB,CAAC,OAAO,CAAC,SAAS,KAAK,OAAO;gBAC5B,CAAC,CAAC,+BAAe,CAAC,KAAK;gBACvB,CAAC,CAAC,+BAAe,CAAC,YAAY,CAAC,IAAI,+BAAe,CAAC,YAAY,CAAC;QACpE,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEM,MAAM;QACX,IAAA,0BAAc,EAAC,IAAI,CAAC,CAAC;QACrB,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACtB,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtC,IAAI,CAAC,UAAU,EAAE,CAAC;YACpB,CAAC;QACH,CAAC;QAED,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC7D,IAAA,iBAAU,EAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;YAClC,CAAC;QACH,CAAC;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAED,oCAAoC;IAC5B,UAAU;QAChB,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAErB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAExB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAChC,IAAI,CAAC,SAAS,CAAC,iCAAiB,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAI,CAAC,SAAS,CAAC,4BAAY,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEtC,IAAA,cAAQ,EAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACrB,CAAC;IAED,oCAAoC;IAC5B,UAAU;QAChB,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;QAEpB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAExB,IAAA,cAAQ,EAAC,IAAI,EAAE,CAAC,CAAC,CAAC;IACpB,CAAC;IAEO,UAAU;QAChB,MAAM,aAAa,GAAI,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAiB,GAAG,CAAC,CAAC;QAChE,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxB,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,OAAwB,EAAE,CAAC;YACvD,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QACD,IAAA,cAAQ,EAAC,IAAI,EAAE,CAAC,GAAG,aAAa,CAAC,CAAC;IACpC,CAAC;IAEO,UAAU;QAChB,MAAM,KAAK,GAAI,IAAI,CAAC,IAAI,CAAC,OAAyB,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE;YAClE,OAAO,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;QAC9B,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACxB,KAAK,MAAM,EAAE,IAAI,KAAK,EAAE,CAAC;YACvB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAW,CAAC,CAAC;QACtC,CAAC;QACD,IAAA,cAAQ,EAAC,IAAI,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IACnC,CAAC;IAED,oCAAoC;IAC5B,UAAU,CAAC,IAAkB;QACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC9B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAExB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QAEtB,IAAA,cAAQ,EAAC,IAAI,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAClC,CAAC;IAEO,UAAU;QAChB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3D,MAAM,YAAY,GAChB,KAAK,IAAI,CAAC;YACR,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;YAC3C,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;QAExD,MAAM,OAAO,GAAG,IAAI,mBAAQ,EAAE,CAAC,YAAY,EAAE,CAAC;QAC9C,IAAI,MAAM,GAAG,CAAC,CAAC;QACf,IAAI,IAAI,CAAC,gBAAgB,KAAK,+BAAe,CAAC,YAAY,EAAE,CAAC;YAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;gBAChC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;gBAClC,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;oBACjB,MAAM,GAAG,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;gBAChE,CAAC;qBAAM,CAAC;oBACN,MAAM,GAAG,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,gBAAgB,KAAK,+BAAe,CAAC,KAAK,EAAE,CAAC;YAC3D,oBAAoB;YACpB,MAAM,GAAG,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QACjE,CAAC;QACD,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,EAAE,CAAC;QACjC,MAAM,UAAU,GAAG,IAAA,cAAO,EAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAEO,UAAU,CAAC,IAAe;QAChC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAExE,MAAM,GAAG,GAAgB;YACvB,KAAK,EAAE,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,OAAO,CAAC;YACxC,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC;YAC3C,QAAQ;YACR,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,KAAK;YACL,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,OAAO,EAAE,IAAI,CAAC,OAAO;SACtB,CAAC;QACF,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,MAAM,YAAY,GAChB,KAAK,GAAG,CAAC;YACP,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,MAAM,GAAG,QAAQ;YAC5D,CAAC,CAAC,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,GAAG,QAAQ,CAAC;QAExC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,KAAK,YAAY,EAAE,CAAC;YACrC,MAAM,IAAI,UAAU,CAClB,0BAA0B,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,YAAY,EAAE,CACtE,CAAC;QACJ,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;CACF;AAtJD,6BAsJC;AAED,SAAS,YAAY,CAAC,KAAa,EAAE,IAAY;IAC/C,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QACzC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,MAAM,IAAI,SAAS,CAAC,GAAG,IAAI,6BAA6B,CAAC,CAAC;AAC5D,CAAC;AAQD,SAAS,YAAY,CACnB,IAAe,EACf,OAAuB;IAEvB,MAAM,EAAE,QAAQ,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC;IACzC,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;QACzE,MAAM,IAAI,UAAU,CAAC,mCAAmC,QAAQ,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,MAAM,WAAW,GAAuB;QACtC,QAAQ;QACR,KAAK;QACL,SAAS,EAAE,yBAAS,CAAC,OAAO;KAC7B,CAAC;IACF,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,CAAC;YACJ,WAAW,CAAC,SAAS,GAAG,yBAAS,CAAC,gBAAgB,CAAC;YACnD,MAAM;QACR,KAAK,CAAC;YACJ,WAAW,CAAC,SAAS,GAAG,yBAAS,CAAC,UAAU,CAAC;YAC7C,MAAM;QACR,KAAK,CAAC;YACJ,IAAI,OAAO,EAAE,CAAC;gBACZ,WAAW,CAAC,SAAS,GAAG,yBAAS,CAAC,cAAc,CAAC;YACnD,CAAC;iBAAM,CAAC;gBACN,WAAW,CAAC,SAAS,GAAG,yBAAS,CAAC,SAAS,CAAC;YAC9C,CAAC;YACD,MAAM;QACR,KAAK,CAAC;YACJ,WAAW,CAAC,SAAS,GAAG,yBAAS,CAAC,eAAe,CAAC;YAClD,MAAM;QACR;YACE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtD,CAAC;IACD,OAAO,WAAW,CAAC;AACrB,CAAC;AAED,SAAS,cAAc,CACrB,IAAkB,EAClB,OAAiB,EACjB,YAAoB,EACpB,MAAc;IAEd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACpC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,mBAAmB,CAC1B,SAAsB,EACtB,IAAkB,EAClB,OAAiB,EACjB,MAAc;IAEd,MAAM,MAAM,GAAG;QACb,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;QAClC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;QAClC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;QAClC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;QAClC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;QAClC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;QAClC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;KACnC,CAAC;IACF,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,SAAS,CAAC;IACrD,IAAI,SAAS,GAAG,CAAC,CAAC;IAClB,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;QACjB,SAAS,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACzC,CAAC;SAAM,CAAC;QACN,SAAS,GAAG,CAAC,QAAQ,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IACD,oBAAoB;IACpB,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,CAAC,EAAE,SAAS,EAAE,EAAE,CAAC;QACnD,MAAM,IAAI,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;QAC/B,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAC1B,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAC/C,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAC3B,CAAC,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAChD,CAAC;QAEF,IAAI,SAAS,IAAI,CAAC,IAAI,UAAU,IAAI,CAAC;YAAE,SAAS;QAChD,MAAM,aAAa,GAAG,SAAS,GAAG,SAAS,CAAC;QAC5C,iCAAiC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;YACvC,4BAA4B;YAC5B,MAAM,WAAW,GACf,KAAK,IAAI,CAAC;gBACR,CAAC,CAAC,IAAI,UAAU,CAAC,aAAa,CAAC;gBAC/B,CAAC,CAAC,IAAI,WAAW,CAAC,aAAa,CAAC,CAAC;YAErC,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;gBACnC,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC;gBACvC,IAAI,MAAM,GAAG,KAAK,IAAI,MAAM,GAAG,MAAM,EAAE,CAAC;oBACtC,MAAM,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK,GAAG,MAAM,CAAC,GAAG,SAAS,CAAC;oBACrD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE,CAAC;wBACnC,WAAW,CAAC,SAAS,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;oBAC9C,CAAC;gBACH,CAAC;YACH,CAAC;YACD,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;YAClC,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;gBAChB,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;YAClC,CAAC;iBAAM,IAAI,KAAK,KAAK,EAAE,EAAE,CAAC;gBACxB,KAAK,MAAM,KAAK,IAAI,WAAW,EAAE,CAAC;oBAChC,OAAO,CAAC,SAAS,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,YAAY;oBACpD,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,eAAe,CACtB,IAAkB,EAClB,OAAiB,EACjB,YAAoB,EACpB,MAAc;IAEd,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,YAAY,EAAE,CAAC,EAAE,EAAE,CAAC;QACtC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACtC,CAAC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC"}