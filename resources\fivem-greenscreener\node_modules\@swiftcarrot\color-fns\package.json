{"name": "@swiftcarrot/color-fns", "version": "3.2.0", "description": "color functions for node and browser", "publishConfig": {"access": "public"}, "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "sideEffects": false, "scripts": {"build": "rollup -c", "test": "flow && jest --coverage", "prepublishOnly": "npm test && npm run build", "watch": "rollup -cw"}, "files": ["dist"], "repository": {"type": "git", "url": "git+https://github.com/swiftcarrot/color-fns.git"}, "keywords": ["color", "css", "functions", "parse", "parser", "utilities"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/swiftcarrot/color-fns/issues"}, "homepage": "https://swiftcarrot.dev/color-fns", "devDependencies": {"@babel/core": "^7.10.3", "babel-jest": "^26.1.0", "babel-preset-swiftcarrot": "^1.1.0", "flow-bin": "^0.128.0", "jest": "^26.1.0", "prettier": "^2.0.5", "rollup": "^2.18.1", "rollup-plugin-babel": "^4.4.0"}, "dependencies": {"@babel/runtime": "^7.10.3"}}