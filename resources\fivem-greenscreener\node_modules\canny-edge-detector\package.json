{"name": "canny-edge-detector", "version": "1.0.0", "description": "Canny edge detector", "main": "lib/index.js", "module": "src/index.js", "files": ["lib", "src"], "scripts": {"eslint": "eslint src", "eslint-fix": "npm run eslint -- --fix", "prepublish": "rollup -c", "test": "run-s testonly eslint", "testonly": "jest"}, "repository": {"type": "git", "url": "https://github.com/image-js/canny-edge-detector.git"}, "keywords": ["image-js", "image", "computer", "vision", "canny", "edge", "detector"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/image-js/canny-edge-detector/issues"}, "homepage": "https://github.com/image-js/canny-edge-detector#readme", "devDependencies": {"babel-plugin-transform-es2015-modules-commonjs": "^6.24.1", "eslint": "^4.1.1", "eslint-config-cheminfo": "^1.6.0", "eslint-plugin-no-only-tests": "^2.0.0", "image-js": "^0.11.4", "jest": "^20.0.4", "npm-run-all": "^4.0.2", "rollup": "^0.43.0"}}